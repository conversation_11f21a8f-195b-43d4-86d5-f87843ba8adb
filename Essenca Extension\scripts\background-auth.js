// /scripts/background-auth.js
// Background-specific authentication functions for Chrome Extension

import { getSession, getValidAccessToken, refreshAccessToken, logoutUser } from './auth.js';

// Enhanced settings retrieval with proper auth handling
export async function getAuthenticatedSettings() {
  const syncPromise = new Promise((resolve) => {
    chrome.storage.sync.get({
      provider: 'essenca_api',
      openaiApiKey: '',
      geminiApiKey: '',
      model: 'gpt-3.5-turbo',
      systemPrompt: '',
      userProfileInfo: ''
    }, (items) => resolve(items));
  });

  const localPromise = new Promise((resolve) => {
    chrome.storage.local.get({ 
      jwtToken: null, 
      essenca_session: null 
    }, (items) => resolve(items));
  });

  const [syncSettings, localSettings] = await Promise.all([syncPromise, localPromise]);
  return { ...syncSettings, ...localSettings };
}

// Background-specific authenticated API request with retry logic
export async function makeAuthenticatedBackgroundRequest(url, options = {}) {
  try {
    // Always get a valid token (handles refresh automatically)
    const accessToken = await getValidAccessToken();
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
      ...options.headers
    };

    const config = {
      method: 'GET',
      ...options,
      headers
    };

    console.log('🔄 Making authenticated background request to:', url);
    const response = await fetch(url, config);

    // Handle 401/403 errors with retry logic
    if (response.status === 401 || response.status === 403) {
      console.log('🔄 Auth error in background, attempting token refresh...');
      
      try {
        const newToken = await refreshAccessToken();
        
        // Retry with new token
        const retryResponse = await fetch(url, {
          ...config,
          headers: {
            ...headers,
            'Authorization': `Bearer ${newToken}`
          }
        });
        
        if (!retryResponse.ok) {
          const errorData = await retryResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Request failed after token refresh');
        }
        
        console.log('✅ Background request succeeded after token refresh');
        return retryResponse;
      } catch (refreshError) {
        console.error('❌ Background token refresh failed:', refreshError);
        await logoutUser();
        throw new Error('Session expired. Please log in again.');
      }
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Background request failed: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error('Background auth request error:', error);
    throw error;
  }
}

// Check if user is authenticated (replacement for the check in background.js)
export async function ensureAuthenticated() {
  try {
    await getValidAccessToken();
    return true;
  } catch (error) {
    throw new Error('You are not logged in. Please log in from the extension settings.');
  }
}

// Background token refresh alarm system
export function initializeBackgroundTokenRefresh() {
  console.log('🔄 Initializing background token refresh system...');
  
  // Create alarm for periodic token refresh (every 30 minutes)
  chrome.alarms.create('tokenRefresh', { periodInMinutes: 30 });
  
  // Listen for alarm events
  chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'tokenRefresh') {
      try {
        const session = await getSession();
        if (session && session.expires_at) {
          console.log('🔄 Background token refresh check...');
          
          // Check if token expires in the next 10 minutes (600000 ms)
          const expiresIn = (session.expires_at * 1000) - Date.now();
          const tenMinutes = 10 * 60 * 1000;
          
          if (expiresIn < tenMinutes) {
            console.log('🔄 Proactively refreshing token in background...');
            console.log(`Token expires in ${Math.round(expiresIn / 1000 / 60)} minutes`);
            await refreshAccessToken();
            console.log('✅ Background token refresh completed');
          } else {
            console.log(`✅ Token is still valid for ${Math.round(expiresIn / 1000 / 60)} minutes`);
          }
        } else {
          console.log('ℹ️ No active session found during background check');
        }
      } catch (error) {
        console.error('❌ Background refresh failed:', error);
        // Don't logout on background refresh failure to avoid disrupting user
      }
    }
  });
  
  console.log('✅ Background token refresh system initialized');
}

// Debug function for background auth status
export async function debugBackgroundAuthStatus() {
  try {
    const session = await getSession();
    const settings = await getAuthenticatedSettings();
    
    console.log('🔍 Background Auth Debug Info:');
    console.log('- Has session:', !!session);
    console.log('- Has jwtToken (legacy):', !!settings.jwtToken);
    console.log('- Has essenca_session:', !!settings.essenca_session);
    
    if (session) {
      const now = Date.now();
      const expiresAt = session.expires_at * 1000;
      const timeUntilExpiry = expiresAt - now;
      
      console.log('- Current time:', new Date(now).toISOString());
      console.log('- Token expires at:', new Date(expiresAt).toISOString());
      console.log('- Time until expiry:', Math.round(timeUntilExpiry / 1000 / 60), 'minutes');
      console.log('- Has refresh token:', !!session.refresh_token);
      console.log('- Access token length:', session.access_token?.length || 0);
    }
  } catch (error) {
    console.error('❌ Background auth debug failed:', error);
  }
}
