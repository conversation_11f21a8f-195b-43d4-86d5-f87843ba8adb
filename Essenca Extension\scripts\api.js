// /scripts/api.js

// Note: You would need to import getValidAccessToken from './auth.js'
// This is a conceptual guide; for actual extensions, use module bundling.

// TODO: Replace with your actual API base URL from environment variables
const API_BASE_URL = 'https://essenca-admin.vercel.app/api';

/**
 * Makes a request to the API with the given endpoint, method, and body.
 * Enhanced with automatic token refresh and retry logic.
 *
 * @param {string} endpoint - The API endpoint to make the request to.
 * @param {string} [method='GET'] - The HTTP method to use for the request.
 * @param {object} [body=null] - The request body.
 * @param {boolean} [isPublic=false] - Whether this is a public endpoint (no auth required).
 * @returns {Promise<object>} The response data.
 */
async function apiRequest(endpoint, method = 'GET', body = null, isPublic = false) {
  try {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (!isPublic) {
      // CRITICAL: Always get a valid token (this handles refresh automatically)
      const accessToken = await getValidAccessToken();
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }
    }

    const config = {
      method,
      headers
    };

    if (body) {
      config.body = JSON.stringify(body);
    }

    console.log('🔄 Making API request to:', `${API_BASE_URL}${endpoint}`);
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    // Handle 401/403 errors by attempting token refresh
    if (!isPublic && (response.status === 401 || response.status === 403)) {
      console.log('🔄 Auth error, attempting token refresh...');

      try {
        const { refreshAccessToken, logoutUser } = await import('./auth.js');
        const newToken = await refreshAccessToken();

        // Retry the request with new token
        const retryResponse = await fetch(`${API_BASE_URL}${endpoint}`, {
          ...config,
          headers: {
            ...headers,
            'Authorization': `Bearer ${newToken}`
          }
        });

        if (!retryResponse.ok) {
          const errorData = await retryResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Request failed after token refresh');
        }

        console.log('✅ API request succeeded after token refresh');
        const contentType = retryResponse.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return retryResponse.json();
        }
        return null;

      } catch (refreshError) {
        console.error('❌ Token refresh failed:', refreshError);
        const { logoutUser } = await import('./auth.js');
        await logoutUser(); // Force logout if refresh fails
        throw new Error('Session expired. Please log in again.');
      }
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `API request to ${endpoint} failed`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json(); // Only parse JSON if the server says it's JSON
    }

    return null; // Return null for non-JSON responses
  } catch (error) {
    console.error('API Request Error:', error);
    throw error;
  }
}

// --- Public Endpoints ---
export const login = (email, password) =>
  apiRequest('/login', 'POST', { email, password }, true);

export const register = (email, username, password) =>
  apiRequest('/register', 'POST', { email, username, password }, true);

// --- Authenticated User Endpoints ---
export const getMyProfile = () => apiRequest('/api/essenca/v1/user/me');
export const getMyBalance = () => apiRequest('/api/essenca/v1/balance');
export const getMyActivity = () => apiRequest('/api/essenca/v1/user/activity');

export const changePassword = (current_password, new_password) =>
  apiRequest('/api/essenca/v1/user/change-password', 'POST', { current_password, new_password });

export const changeUsername = (password, new_username) =>
  apiRequest('/api/essenca/v1/user/change-username', 'POST', { password, new_username });

// --- Core AI Endpoint ---
export const processContent = (action, content, options = {}) =>
  apiRequest('/api/essenca/v1/process', 'POST', { action, content, ...options });

// --- Admin Endpoints ---
export const getAllUsers = () => apiRequest('/api/admin/users');
export const createAdminUser = (email, password, username) =>
  apiRequest('/api/admin/users/create', 'POST', { email, password, username });
