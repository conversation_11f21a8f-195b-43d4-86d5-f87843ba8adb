// /scripts/api.js

// Note: You would need to import getValidAccessToken from './auth.js'
// This is a conceptual guide; for actual extensions, use module bundling.

// TODO: Replace with your actual API base URL from environment variables
const API_BASE_URL = 'https://essenca-admin.vercel.app/api';

/**
 * Makes a request to the API with the given endpoint, method, and body.
 * 
 * @param {string} endpoint - The API endpoint to make the request to.
 * @param {string} [method='GET'] - The HTTP method to use for the request.
 * @param {object} [body=null] - The request body.
 * @returns {Promise<object>} The response data.
 */
async function apiRequest(endpoint, method = 'GET', body = null, isPublic = false) {
  const headers = {
    'Content-Type': 'application/json',
  };

  if (!isPublic) {
    const accessToken = await getValidAccessToken();
    if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
    }
  }

  const config = {
    method,
    headers
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `API request to ${endpoint} failed`);
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json(); // Only parse JSON if the server says it's JSON
  }

  return null; // Return null for non-JSON responses
}

// --- Public Endpoints ---
export const login = (email, password) =>
  apiRequest('/login', 'POST', { email, password }, true);

export const register = (email, username, password) => 
  apiRequest('/register', 'POST', { email, username, password }, true);

// --- Authenticated User Endpoints ---
export const getMyProfile = () => apiRequest('/api/essenca/v1/user/me');
export const getMyBalance = () => apiRequest('/api/essenca/v1/balance');
export const getMyActivity = () => apiRequest('/api/essenca/v1/user/activity');

export const changePassword = (current_password, new_password) => 
  apiRequest('/api/essenca/v1/user/change-password', 'POST', { current_password, new_password });

export const changeUsername = (password, new_username) => 
  apiRequest('/api/essenca/v1/user/change-username', 'POST', { password, new_username });

// --- Core AI Endpoint ---
export const processContent = (action, content, options = {}) => 
  apiRequest('/api/essenca/v1/process', 'POST', { action, content, ...options });

// --- Admin Endpoints ---
export const getAllUsers = () => apiRequest('/api/admin/users');
export const createAdminUser = (email, password, username) => 
  apiRequest('/api/admin/users/create', 'POST', { email, password, username });
