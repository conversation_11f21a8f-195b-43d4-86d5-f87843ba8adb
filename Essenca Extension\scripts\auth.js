// /scripts/auth.js

// IMPORTANT: Replace with your actual Supabase details
const SUPABASE_URL = 'https://dnngormeluqzeiwjzyhm.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRubmdvcm1lbHVxemVpd2p6eWhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzY1NjksImV4cCI6MjA2ODg1MjU2OX0.Q2dUR5ojNxq6vfqKNlNKb3byX4ODypgQ44IMDQa-BGs';

// Logs the user out by clearing the session
export async function logoutUser() {
  await chrome.storage.local.remove('essenca_session');
}

// Retrieves the current session from storage
export async function getSession() {
  const { essenca_session } = await chrome.storage.local.get('essenca_session');
  return essenca_session;
}

// Refreshes the access token using the refresh token
export async function refreshAccessToken() {
  const session = await getSession();
  if (!session?.refresh_token) throw new Error('No refresh token available.');

  const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_ANON_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ refresh_token: session.refresh_token })
  });

  if (!response.ok) {
    await logoutUser(); // If refresh fails, log the user out
    throw new Error('Session expired. Please log in again.');
  }

  const data = await response.json();
  // Update the session with the new token data
  await chrome.storage.local.set({ 'essenca_session': { ...session, ...data } });
  return data.access_token;
}

// Gets a valid access token, refreshing if necessary
export async function getValidAccessToken() {
  const session = await getSession();
  if (!session) throw new Error('User not authenticated.');

  // Check if token expires in the next 60 seconds
  const isExpired = (session.expires_at * 1000) - Date.now() < 60000;
  if (isExpired) {
    return await refreshAccessToken();
  }
  return session.access_token;
}
